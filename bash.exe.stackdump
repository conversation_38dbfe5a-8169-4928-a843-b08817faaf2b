Stack trace:
Frame         Function      Args
0007FFFFB760  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB760, 0007FFFFA660) msys-2.0.dll+0x1FE8E
0007FFFFB760  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA38) msys-2.0.dll+0x67F9
0007FFFFB760  000210046832 (000210286019, 0007FFFFB618, 0007FFFFB760, 000000000000) msys-2.0.dll+0x6832
0007FFFFB760  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB760  000210068E24 (0007FFFFB770, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA40  00021006A225 (0007FFFFB770, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA4BC10000 ntdll.dll
7FFA4A230000 KERNEL32.DLL
7FFA495F0000 KERNELBASE.dll
7FFA4ABF0000 USER32.dll
7FFA49250000 win32u.dll
7FFA4BA10000 GDI32.dll
7FFA49280000 gdi32full.dll
7FFA493A0000 msvcp_win.dll
7FFA49470000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA4BB10000 advapi32.dll
7FFA49E00000 msvcrt.dll
7FFA49D50000 sechost.dll
7FFA49440000 bcrypt.dll
7FFA4A3E0000 RPCRT4.dll
7FFA48910000 CRYPTBASE.DLL
7FFA49AC0000 bcryptPrimitives.dll
7FFA4A570000 IMM32.DLL
